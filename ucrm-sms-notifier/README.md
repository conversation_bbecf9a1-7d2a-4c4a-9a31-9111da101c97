# UCRM SMS Notifier Plugin

SMS notifikácie pre UCRM systém cez SMSGate.sk API. Plugin podporuje technické SMS pre technikov, automatické SMS pre zákazníkov a hromadné SMS podľa lokácie zariadení.

## Funkcionality

### 🔧 Technické SMS
- **Nové tickety** - SMS technikom pri vytvorení ticketu
- **Zmeny ticketov** - SMS pri zmene stavu alebo priority
- **Výpadky zariadení** - SMS pri výpadkoch z NMS monitoringu
- **Kritické udalosti** - Zvýraznenie kritických správ

### 👥 Zákaznícke SMS - Automatické
- **Faktúry** - SMS o zaslanej faktúre na email
- **Upomienky** - SMS o zaslanej upomienke
- **Platby** - SMS o prijatej platbe
- **Služby** - SMS o zmenách služieb (pozastavenie, aktivácia)

### 📡 Hromadné SMS podľa lokácie
- **Výber zariadenia** - SMS všetkým zákazníkom na vybranom zariadení
- **Vlastná správa** - Prispôsobiteľný text správy
- **Údržba/výpadky** - Informovanie o plánovaných prácach

## Požiadavky

- **UCRM verzia:** 2.1.0 alebo vyššia
- **SMSGate.sk účet** s API tokenom
- **PHP 7.1+**
- **UCRM Plugin SDK 0.9**

## Inštalácia

### 1. Upload pluginu
1. Stiahnite `ucrm-sms-notifier.zip`
2. V UCRM prejdite do **System → Plugins**
3. Kliknite na **Upload plugin**
4. Vyberte ZIP súbor a nahrajte
5. **Enable** plugin

### 2. Konfigurácia
1. V zozname pluginov nájdite **SMS Notifier**
2. Kliknite na **Configuration**
3. Vyplňte povinné polia:

#### Základné nastavenia:
- **SMSGate.sk API Token** - váš API token zo SMSGate.sk
- **SMSGate.sk API URL** - predvolené: `https://api.smsgate.sk/v2/send`

#### Technické SMS:
- **Telefónne čísla technikov** - čísla oddelené čiarkou (napr. `+421901234567,+421902345678`)
- **Povoliť SMS pre tickety** - zapnúť SMS pri ticketoch
- **Povoliť SMS pre výpadky zariadení** - zapnúť SMS pri výpadkoch z NMS

#### Zákaznícke SMS:
- **Povoliť automatické SMS zákazníkom** - zapnúť automatické SMS
- **Povoliť hromadné SMS** - zapnúť hromadné SMS podľa lokácie

#### Dodatočné nastavenia:
- **Len v pracovnom čase** - SMS len 8:00-18:00, pondelok-piatok
- **NMS Server URL** - URL NMS servera pre monitoring výpadkov
- **NMS API Token** - token pre NMS API

### 3. SMSGate.sk API Token
Ako získať API token:
1. Prihláste sa na [SMSGate.sk](https://www.smsgate.sk)
2. Prejdite do sekcie **API**
3. Vygenerujte nový **API token**
4. Skopírujte token do konfigurácie pluginu

## Použitie

### Dashboard
Po inštalácii a konfigurácii prejdite do **SMS Notifier** v UCRM menu:

- **Štatistiky** - prehľad konfigurácie a stavu
- **Test SMS** - otestovanie odosielania SMS
- **Hromadné SMS** - odoslanie SMS podľa zariadenia
- **Informácie** - dokumentácia funkcionalít

### Custom atribúty zákazníkov
Pre zákaznícke SMS vytvorte tieto custom atribúty:

1. **System → Customization → Custom attributes**
2. **Add attribute** pre **Client**:

| Atribút | Typ | Popis |
|---------|-----|-------|
| `sms_enabled` | Checkbox | Hlavný prepínač SMS |
| `sms_invoices` | Checkbox | SMS pri faktúrach |
| `sms_reminders` | Checkbox | SMS pri upomienkach |
| `sms_service_alerts` | Checkbox | SMS pri zmenách služieb |

### Automatické SMS triggery

#### Technické SMS:
```bash
# Test SMS
php main.php test_sms +421901234567

# Nový ticket
php main.php ticket_created 123 "Výpadok internetu" high

# Výpadok zariadenia  
php main.php device_outage "AP-Centrum" "access-point" "Bratislava"
```

#### Zákaznícke SMS:
```bash
# SMS o faktúre
php main.php customer_invoice +421901234567 "2025001234" "45.50"
```

## Integrácia s UCRM

### Webhooks
Plugin môže byť integrovaný s UCRM webhookmi:

1. **System → Webhooks**
2. **Add webhook** pre požadované udalosti
3. **URL:** `https://your-server.com/path/to/plugin/main.php`

### NMS integrácia
Pre monitoring výpadkov zariadení:

1. Nastavte **NMS Server URL** a **NMS API Token**
2. Plugin automaticky monitoruje stav zariadení
3. SMS sa odošle pri výpadku zariadenia

## Príklady SMS správ

### Technické SMS:
- `UCRM: Nový ticket #123 - Výpadok internetu`
- `KRITICKÝ! UCRM: Nový ticket #456 - Server nedostupný`
- `UCRM: Výpadok access-point 'AP-Centrum' na lokalite Bratislava`

### Zákaznícke SMS:
- `UCRM: Na váš email sme zaslali faktúru č. 2025001234 v sume 45.50€. Ďakujeme.`
- `UCRM: Bola vám zaslaná upomienka za faktúru č. 2025001234. Prosím uhraďte do 7 dní.`
- `UCRM: Na vašej adrese sa bude realizovať odstávka internetových služieb z dôvodu rekonštrukcie siete.`

## Riešenie problémov

### Plugin sa nenačíta
- Skontrolujte, či je `manifest.json` v root adresári ZIP súboru
- Overte, že `vendor/` adresár je zahrnutý v ZIP súbore
- Skontrolujte UCRM logy v **System → Logs**

### SMS sa neodosielajú
- Overte SMSGate.sk API token v konfigurácii
- Skontrolujte kredit na SMSGate.sk účte
- Použite **Test SMS** funkciu v plugine
- Skontrolujte plugin logy v UCRM

### Hromadné SMS nefunguje
- Zapnite **Povoliť hromadné SMS** v konfigurácii
- Skontrolujte, či zákazníci majú `sms_enabled` atribút nastavený na `1`
- Overte, že zákazníci majú vyplnené telefónne čísla

### Chyby v logoch
Časté chyby a riešenia:

| Chyba | Riešenie |
|-------|----------|
| `API token nie je nakonfigurovaný` | Vyplňte SMSGate.sk API token |
| `SMSGate.sk API chyba: Invalid token` | Skontrolujte správnosť API tokenu |
| `Chýba telefónne číslo` | Vyplňte telefónne čísla technikov |
| `Insufficient credit` | Dobite kredit na SMSGate.sk účte |

## Podpora

### Dokumentácia
- [SMSGate.sk API dokumentácia](https://www.smsgate.sk/api)
- [UCRM Plugin SDK](https://github.com/Ubiquiti-App/UCRM-Plugin-SDK)

### Kontakt
Pre technickú podporu kontaktujte administrátora UCRM systému.

## Changelog

### v1.0.0 (6.8.2025)
- ✅ Základná funkcionalita SMS odosielania
- ✅ SMSGate.sk API integrácia
- ✅ Technické SMS pre tickety a výpadky
- ✅ Zákaznícke SMS s custom atribútmi
- ✅ Hromadné SMS podľa zariadení
- ✅ Webové rozhranie s dashboardom
- ✅ Test SMS funkcionalita
- ✅ Pracovný čas obmedzenia
- ✅ UCRM Plugin SDK 0.9 kompatibilita

## Licencia

Tento plugin je vytvorený pre UCRM systém a používa UCRM Plugin SDK.
