<?php

chdir(__DIR__);

require_once __DIR__ . '/vendor/autoload.php';

/**
 * UCRM SMS Notifier - Public interface
 * Webové rozhranie pre SMS dashboard a hromadné SMS
 */

// Získanie UCRM služieb
$log = \Ubnt\UcrmPluginSdk\Service\PluginLogManager::create();
$security = \Ubnt\UcrmPluginSdk\Service\UcrmSecurity::create();
$configManager = \Ubnt\UcrmPluginSdk\Service\PluginConfigManager::create();
$ucrmApi = \Ubnt\UcrmPluginSdk\Service\UcrmApi::create();

// Kontrola prihlásenia používateľa
$user = $security->getUser();
if (!$user) {
    die('<h1>Chyba</h1><p>Nie ste prihlásený do UCRM systému.</p>');
}

$log->appendLog('SMS Notifier - Frontend otvorený používateľom: ' . $user->username);

// Inicializácia premenných
$errorMessage = null;
$successMessage = null;
$devices = [];
$customers = [];

// Načítanie konfigurácie z UCRM
$config = $configManager->loadConfig();
$smsApiToken = $config['smsgate_api_token'] ?? '';
$smsApiUrl = $config['smsgate_api_url'] ?? 'https://api.smsgate.sk/v2/send';
$enableBulkSms = !empty($config['enable_bulk_sms']);

// Kontrola konfigurácie
if (empty($smsApiToken)) {
    $errorMessage = 'Plugin nie je nakonfigurovaný. Prejdite do System → Plugins → SMS Notifier → Configuration a nastavte SMSGate.sk API token.';
}

// Funkcia pre GET request s cURL
function makeGetRequest($url, $log) {
    $log->appendLog("SMS Debug - GET Request: {$url}");

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if ($response === false || !empty($curlError)) {
        throw new Exception("cURL chyba: {$curlError}");
    }

    $log->appendLog("SMS Debug - HTTP Code: {$httpCode}");
    $log->appendLog("SMS Debug - Response: " . substr($response, 0, 500) . (strlen($response) > 500 ? '...' : ''));

    if ($httpCode !== 200) {
        throw new Exception("HTTP chyba: {$httpCode}");
    }

    $result = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Chyba pri parsovaní JSON odpovede: ' . json_last_error_msg());
    }

    return $result;
}

// Funkcia pre zistenie IP adresy UCRM servera
function getServerIpAddress($log) {
    $ipServices = [
        'https://api.ipify.org?format=json',
        'https://httpbin.org/ip',
        'https://api.myip.com',
        'https://ipinfo.io/json'
    ];
    
    foreach ($ipServices as $service) {
        try {
            $log->appendLog("IP Debug - Skúšam službu: {$service}");
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $service);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'User-Agent: UCRM-SMS-Notifier/1.0'
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            if ($response !== false && empty($curlError) && $httpCode === 200) {
                $result = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // Rôzne služby majú rôzne formáty odpovede
                    $ip = null;
                    if (isset($result['ip'])) {
                        $ip = $result['ip'];
                    } elseif (isset($result['origin'])) {
                        $ip = $result['origin'];
                    } elseif (isset($result['query'])) {
                        $ip = $result['query'];
                    }
                    
                    if ($ip && filter_var($ip, FILTER_VALIDATE_IP)) {
                        $log->appendLog("IP Debug - Zistená IP adresa: {$ip} (služba: {$service})");
                        return $ip;
                    }
                }
            }
            
            $log->appendLog("IP Debug - Služba {$service} zlyhala: HTTP {$httpCode}, Error: {$curlError}");
        } catch (Exception $e) {
            $log->appendLog("IP Debug - Chyba pri službe {$service}: " . $e->getMessage());
        }
    }
    
    // Fallback - skúsime získať IP z $_SERVER
    $serverIp = $_SERVER['SERVER_ADDR'] ?? $_SERVER['LOCAL_ADDR'] ?? null;
    if ($serverIp && filter_var($serverIp, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE)) {
        $log->appendLog("IP Debug - Fallback IP zo servera: {$serverIp}");
        return $serverIp;
    }
    
    $log->appendLog("IP Debug - Nepodarilo sa zistiť IP adresu servera");
    return null;
}

// Funkcia pre overenie telefónneho čísla
function checkPhoneNumber($apiToken, $phoneNumber, $log) {
    $url = 'https://api.smsgate.sk/json/check_phone_number?' . http_build_query([
        'token' => $apiToken,
        'phone_number' => $phoneNumber
    ]);
    
    try {
        $result = makeGetRequest($url, $log);
        
        if (isset($result['result']['status']) && $result['result']['status'] === 'success') {
            $exists = $result['exists'] ?? false;
            $operator = $result['operator'] ?? '';
            $log->appendLog("SMS Debug - Phone check: exists={$exists}, operator={$operator}");
            return ['exists' => $exists, 'operator' => $operator];
        } else {
            $error = $result['result']['description'] ?? $result['result']['code'] ?? 'Neznáma chyba';
            throw new Exception("Phone check chyba: {$error}");
        }
    } catch (Exception $e) {
        $log->appendLog("SMS Debug - Phone check zlyhalo: " . $e->getMessage());
        // Pokračujeme aj bez phone check
        return ['exists' => true, 'operator' => 'unknown'];
    }
}

// Funkcia pre získanie session_id cez auth (fallback)
function getSessionId($apiToken, $log) {
    $url = 'https://api.smsgate.sk/json/auth?' . http_build_query([
        'token' => $apiToken
    ]);
    
    $result = makeGetRequest($url, $log);
    
    if (isset($result['result']['status']) && $result['result']['status'] === 'success') {
        return $result['session_id'];
    } else {
        $error = $result['result']['description'] ?? $result['result']['code'] ?? 'Neznáma chyba';
        throw new Exception("SMSGate.sk Auth chyba: {$error}");
    }
}

// Funkcia pre odosielanie SMS cez SMSGate.sk API (GET requesty)
function sendSMS($apiToken, $phoneNumber, $message, $log) {
    $log->appendLog("SMS Debug - Starting SMS send to: {$phoneNumber}");

    // 1. Overenie telefónneho čísla (voliteľné)
    $phoneCheck = checkPhoneNumber($apiToken, $phoneNumber, $log);
    if (!$phoneCheck['exists']) {
        $log->appendLog("SMS Debug - Warning: Phone number may not exist");
    }

    // 2. Token-based prístup (priorita)
    $url = 'https://api.smsgate.sk/json/send_message?' . http_build_query([
        'token' => $apiToken,
        'to' => $phoneNumber,
        'text' => $message,
        'from' => 'UCRM'
    ]);
    
    try {
        $result = makeGetRequest($url, $log);
        
        // Kontrola odpovede
        if (isset($result['result']['status']) && $result['result']['status'] === 'success') {
            // Kontrola jednotlivých správ
            if (isset($result['messages']) && is_array($result['messages'])) {
                $firstMessage = $result['messages'][0];
                if ($firstMessage['status'] === 'success') {
                    $messageId = $firstMessage['message_id'];
                    $parts = $firstMessage['parts'];
                    $log->appendLog("SMS úspešne odoslané na {$phoneNumber} (ID: {$messageId}, časti: {$parts}): {$message}");
                    return true;
                } else {
                    $error = $firstMessage['description'] ?? $firstMessage['code'] ?? 'Neznáma chyba';
                    throw new Exception("SMS chyba: {$error}");
                }
            } else {
                throw new Exception("Neočakávaný formát odpovede - chýbajú messages");
            }
        } else {
            $error = $result['result']['description'] ?? $result['result']['code'] ?? 'Neznáma chyba';
            throw new Exception("SMSGate.sk API chyba: {$error}");
        }
    } catch (Exception $e) {
        $log->appendLog("SMS Debug - Token-based prístup zlyhal: " . $e->getMessage());
        
        // 3. Session-based fallback
        try {
            $log->appendLog("SMS Debug - Skúšam session-based fallback");
            $sessionId = getSessionId($apiToken, $log);
            $log->appendLog("SMS Debug - Session ID získané: " . substr($sessionId, 0, 8) . "...");
            
            $url = 'https://api.smsgate.sk/json/send_message?' . http_build_query([
                'session_id' => $sessionId,
                'to' => $phoneNumber,
                'text' => $message,
                'from' => 'UCRM'
            ]);
            
            $result = makeGetRequest($url, $log);
            
            if (isset($result['result']['status']) && $result['result']['status'] === 'success') {
                if (isset($result['messages']) && is_array($result['messages'])) {
                    $firstMessage = $result['messages'][0];
                    if ($firstMessage['status'] === 'success') {
                        $messageId = $firstMessage['message_id'];
                        $parts = $firstMessage['parts'];
                        $log->appendLog("SMS úspešne odoslané na {$phoneNumber} (session-based, ID: {$messageId}, časti: {$parts}): {$message}");
                        return true;
                    } else {
                        $error = $firstMessage['description'] ?? $firstMessage['code'] ?? 'Neznáma chyba';
                        throw new Exception("SMS chyba (session-based): {$error}");
                    }
                } else {
                    throw new Exception("Neočakávaný formát odpovede - chýbajú messages (session-based)");
                }
            } else {
                $error = $result['result']['description'] ?? $result['result']['code'] ?? 'Neznáma chyba';
                throw new Exception("SMSGate.sk API chyba (session-based): {$error}");
            }
        } catch (Exception $sessionError) {
            $log->appendLog("SMS Debug - Session-based fallback tiež zlyhal: " . $sessionError->getMessage());
            throw new Exception("Oba prístupy zlyhali. Token-based: " . $e->getMessage() . "; Session-based: " . $sessionError->getMessage());
        }
    }
}

// Spracovanie callback z SMSGate.sk
if ($_GET && isset($_GET['sms_callback'])) {
    $messageId = $_GET['message_id'] ?? null;
    $status = $_GET['status'] ?? null;
    $addressFrom = $_GET['addressFrom'] ?? null;
    $addressTo = $_GET['addressTo'] ?? null;
    $channel = $_GET['channel'] ?? 'sms';
    
    if ($messageId && $status) {
        $log->appendLog("SMS Callback - Message ID: {$messageId}, Status: {$status}, From: {$addressFrom}, To: {$addressTo}, Channel: {$channel}");
        
        // Odpoveď pre SMSGate.sk
        http_response_code(200);
        echo "OK";
        exit;
    }
}

// Spracovanie formulárov
if ($_POST && !$errorMessage) {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'test_sms':
                    $testNumber = $_POST['test_number'] ?? '';
                    if (empty($testNumber)) {
                        throw new Exception('Zadajte telefónne číslo pre test');
                    }
                    
                    $testMessage = 'UCRM SMS Notifier - Test správa ' . date('d.m.Y H:i:s');
                    sendSMS($smsApiToken, $testNumber, $testMessage, $log);
                    $successMessage = "Test SMS úspešne odoslané na číslo {$testNumber}";
                    break;
                    
                case 'check_ip':
                    $serverIp = getServerIpAddress($log);
                    if ($serverIp) {
                        $successMessage = "IP adresa UCRM servera: <strong>{$serverIp}</strong><br><br>
                                         <strong>Postup pridania IP adresy do SMSGate.sk:</strong><br>
                                         1. Prihláste sa na <a href='https://portal.smsgate.sk' target='_blank'>https://portal.smsgate.sk</a><br>
                                         2. Prejdite do <strong>SMS → API kľúč</strong><br>
                                         3. Editujte váš API kľúč<br>
                                         4. Do poľa <strong>'Povoliť prístup na API len z doleuvedených IP adries'</strong> pridajte: <code>{$serverIp}</code><br>
                                         5. Kliknite na <strong>Uložiť</strong><br>
                                         6. Otestujte SMS znovu";
                    } else {
                        $errorMessage = "Nepodarilo sa zistiť IP adresu UCRM servera. Skontrolujte logy pre viac detailov.";
                    }
                    break;
                    
                case 'bulk_sms':
                    if (!$enableBulkSms) {
                        throw new Exception('Hromadné SMS nie sú povolené v konfigurácii');
                    }
                    
                    $deviceId = $_POST['device_id'] ?? '';
                    $message = $_POST['bulk_message'] ?? '';
                    
                    if (empty($deviceId) || empty($message)) {
                        throw new Exception('Vyberte zariadenie a zadajte správu');
                    }
                    
                    // Získanie zákazníkov pre vybrané zariadenie
                    $endpoint = "devices/{$deviceId}/services";
                    $services = $ucrmApi->get($endpoint);
                    
                    $sentCount = 0;
                    $errorCount = 0;
                    
                    foreach ($services as $service) {
                        if (isset($service['clientId'])) {
                            // Získanie zákazníka
                            $client = $ucrmApi->get("clients/{$service['clientId']}");
                            
                            // Kontrola SMS atribútov zákazníka
                            $smsEnabled = false;
                            if (isset($client['attributes'])) {
                                foreach ($client['attributes'] as $attr) {
                                    if ($attr['key'] === 'sms_enabled' && $attr['value'] === '1') {
                                        $smsEnabled = true;
                                        break;
                                    }
                                }
                            }
                            
                            if ($smsEnabled && !empty($client['phone'])) {
                                try {
                                    sendSMS($smsApiToken, $client['phone'], $message, $log);
                                    $sentCount++;
                                } catch (Exception $e) {
                                    $log->appendLog('Chyba pri odosielaní SMS zákazníkovi ' . $client['firstName'] . ' ' . $client['lastName'] . ': ' . $e->getMessage());
                                    $errorCount++;
                                }
                            }
                        }
                    }
                    
                    $successMessage = "Hromadné SMS dokončené. Odoslané: {$sentCount}, Chyby: {$errorCount}";
                    break;
            }
        }
    } catch (Exception $e) {
        $errorMessage = $e->getMessage();
        $log->appendLog('SMS Notifier - Chyba v public.php: ' . $errorMessage);
    }
}

// Načítanie zariadení pre hromadné SMS
if (!$errorMessage && $enableBulkSms) {
    try {
        $devices = $ucrmApi->get('devices');
    } catch (Exception $e) {
        $log->appendLog('SMS Notifier - Chyba pri načítavaní zariadení: ' . $e->getMessage());
    }
}

?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS Notifier</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .config-section { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .sms-card { border-left: 4px solid #007bff; }
        .stats-card { text-align: center; }
        .test-section { background-color: #e8f4fd; border: 1px solid #3498db; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .bulk-section { background-color: #fff3cd; border: 1px solid #ffc107; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2><i class="fas fa-sms"></i> SMS Notifier</h2>
                    <div>
                        <button class="btn btn-primary btn-sm" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Obnoviť
                        </button>
                    </div>
                </div>
                
                <?php if ($errorMessage): ?>
                <!-- Chybová hláška -->
                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle"></i> Chyba</h4>
                    <p><?= htmlspecialchars($errorMessage) ?></p>
                    <?php if (empty($smsApiToken)): ?>
                    <hr>
                    <p class="mb-0">
                        <strong>Postup konfigurácie:</strong><br>
                        1. Prejdite do <strong>System → Plugins</strong><br>
                        2. Nájdite <strong>SMS Notifier</strong><br>
                        3. Kliknite na <strong>Configuration</strong><br>
                        4. Vyplňte <strong>SMSGate.sk API Token</strong><br>
                        5. Kliknite na <strong>Uložiť</strong>
                    </p>
                    <?php endif; ?>
                </div>
                
                <?php elseif ($successMessage): ?>
                <!-- Úspešná hláška -->
                <div class="alert alert-success">
                    <h4><i class="fas fa-check-circle"></i> Úspech</h4>
                    <p><?= htmlspecialchars($successMessage) ?></p>
                </div>
                <?php endif; ?>
                
                <?php if (!$errorMessage): ?>
                <!-- Dashboard -->
                
                <!-- Štatistiky -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card sms-card stats-card">
                            <div class="card-body">
                                <h5 class="card-title text-primary">
                                    <i class="fas fa-cog"></i>
                                </h5>
                                <h6 class="card-subtitle mb-2 text-muted">Konfigurácia</h6>
                                <p class="card-text">
                                    <small>
                                        API: <?= !empty($smsApiToken) ? '<span class="text-success">Nakonfigurované</span>' : '<span class="text-danger">Chýba</span>' ?><br>
                                        Technické SMS: <?= !empty($config['enable_ticket_sms']) ? '<span class="text-success">Zapnuté</span>' : '<span class="text-muted">Vypnuté</span>' ?><br>
                                        Zákaznícke SMS: <?= !empty($config['enable_customer_sms']) ? '<span class="text-success">Zapnuté</span>' : '<span class="text-muted">Vypnuté</span>' ?>
                                    </small>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card sms-card stats-card">
                            <div class="card-body">
                                <h5 class="card-title text-info">
                                    <i class="fas fa-users-cog"></i>
                                </h5>
                                <h6 class="card-subtitle mb-2 text-muted">Technici</h6>
                                <p class="card-text">
                                    <?php 
                                    $techNumbers = explode(',', $config['tech_phone_numbers'] ?? '');
                                    $techCount = count(array_filter(array_map('trim', $techNumbers)));
                                    echo $techCount . ' čísel';
                                    ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card sms-card stats-card">
                            <div class="card-body">
                                <h5 class="card-title text-warning">
                                    <i class="fas fa-broadcast-tower"></i>
                                </h5>
                                <h6 class="card-subtitle mb-2 text-muted">Zariadenia</h6>
                                <p class="card-text"><?= count($devices) ?> zariadení</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card sms-card stats-card">
                            <div class="card-body">
                                <h5 class="card-title text-success">
                                    <i class="fas fa-clock"></i>
                                </h5>
                                <h6 class="card-subtitle mb-2 text-muted">Pracovný čas</h6>
                                <p class="card-text">
                                    <?= !empty($config['working_hours_only']) ? 'Obmedzené' : 'Neobmedzené' ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Test SMS -->
                <div class="test-section">
                    <h4><i class="fas fa-vial"></i> Test SMS</h4>
                    <p>Otestujte odosielanie SMS správ cez SMSGate.sk API s automatickým overením telefónneho čísla.</p>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="test_sms">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="test_number" class="form-label">Telefónne číslo:</label>
                                <input type="tel" class="form-control" id="test_number" name="test_number" 
                                       placeholder="+421901234567" required>
                                <div class="form-text">Zadajte číslo vo formáte +421901234567</div>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-paper-plane"></i> Odoslať test SMS
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <div class="mt-3">
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="check_ip">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-network-wired"></i> Zistiť IP adresu servera
                            </button>
                        </form>
                        <small class="text-muted ms-3">
                            Ak dostávate chybu "NO_ROUTE_FOUND", možno je problém s IP obmedzením v SMSGate.sk
                        </small>
                    </div>
                </div>
                
                <!-- IP Diagnostika -->
                <?php if (isset($_POST['action']) && $_POST['action'] === 'check_ip'): ?>
                <div class="alert alert-info">
                    <h5><i class="fas fa-network-wired"></i> IP Diagnostika</h5>
                    <p>Skontrolujte UCRM logy pre detailné informácie o zisťovaní IP adresy.</p>
                </div>
                <?php endif; ?>
                
                <div class="test-section" style="background-color: #f8f9fa; border: 1px solid #dee2e6;">
                    <h5><i class="fas fa-exclamation-triangle text-warning"></i> Riešenie "NO_ROUTE_FOUND" chyby</h5>
                    <p><strong>Ak dostávate chybu "Nie je možné nájsť žiadny smer pre číslo":</strong></p>
                    
                    <h6 class="mt-3"><i class="fas fa-network-wired"></i> 1. Nastavenie IP adresy:</h6>
                    <ol class="small">
                        <li>Kliknite na <strong>"Zistiť IP adresu servera"</strong> vyššie</li>
                        <li>Skopírujte zobrazenú IP adresu</li>
                        <li>Prihláste sa na <a href="https://portal.smsgate.sk" target="_blank">https://portal.smsgate.sk</a></li>
                        <li>Prejdite do <strong>SMS → API kľúč</strong></li>
                        <li>Editujte váš API kľúč</li>
                        <li>Do poľa <strong>"Povoliť prístup na API len z doleuvedených IP adries"</strong> pridajte IP adresu UCRM servera</li>
                    </ol>
                    
                    <h6 class="mt-3"><i class="fas fa-link"></i> 2. Nastavenie Callback URL:</h6>
                    <div class="alert alert-warning small">
                        <strong>DÔLEŽITÉ:</strong> SMSGate.sk môže vyžadovať callback URL pre delivery statusy!
                    </div>
                    <ol class="small">
                        <li>V tom istom API kľúči nájdite pole <strong>"URL pre callback pre odoslanie delivery statusu"</strong></li>
                        <li>Zadajte túto URL: <code id="callback-url"><?= $_SERVER['HTTP_HOST'] ?? 'your-ucrm-domain.com' ?><?= $_SERVER['REQUEST_URI'] ?>?sms_callback=1</code></li>
                        <li>Kliknite na <strong>Uložiť</strong></li>
                        <li>Otestujte SMS znovu</li>
                    </ol>
                    
                    <div class="mt-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="copyCallbackUrl()">
                            <i class="fas fa-copy"></i> Kopírovať Callback URL
                        </button>
                    </div>
                    
                    <h6 class="mt-3"><i class="fas fa-credit-card"></i> 3. Skontrolovať kredit a limity:</h6>
                    <ul class="small">
                        <li>Zostatok kreditu na účte</li>
                        <li>Mesačný limit API kľúča (aktuálne: 10 SMS)</li>
                        <li>Dostupné SMS smery pre operátorov</li>
                    </ul>
                </div>
                
                <script>
                function copyCallbackUrl() {
                    const url = document.getElementById('callback-url').textContent;
                    navigator.clipboard.writeText(url).then(function() {
                        alert('Callback URL skopírovaná do schránky!');
                    });
                }
                </script>
                
</div>
                
                <?php if ($enableBulkSms): ?>
                <!-- Hromadné SMS -->
                <div class="bulk-section">
                    <h4><i class="fas fa-broadcast-tower"></i> Hromadné SMS podľa zariadenia</h4>
                    <p>Odošlite SMS všetkým zákazníkom pripojeným na vybrané zariadenie.</p>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="bulk_sms">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="device_id" class="form-label">Zariadenie:</label>
                                <select class="form-select" id="device_id" name="device_id" required>
                                    <option value="">Vyberte zariadenie...</option>
                                    <?php foreach ($devices as $device): ?>
                                        <option value="<?= htmlspecialchars($device['id']) ?>">
                                            <?= htmlspecialchars($device['name'] ?? 'Zariadenie #' . $device['id']) ?>
                                            <?php if (!empty($device['site']['name'])): ?>
                                                - <?= htmlspecialchars($device['site']['name']) ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="bulk_message" class="form-label">SMS správa:</label>
                                <textarea class="form-control" id="bulk_message" name="bulk_message" 
                                          rows="3" maxlength="160" required 
                                          placeholder="Váša správa pre zákazníkov..."></textarea>
                                <div class="form-text">Maximálne 160 znakov</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="submit" class="btn btn-warning" 
                                    onclick="return confirm('Naozaj chcete odoslať hromadné SMS všetkým zákazníkom na vybranom zariadení?')">
                                <i class="fas fa-bullhorn"></i> Odoslať hromadné SMS
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Poznámka:</strong> SMS sa odošle len zákazníkom, ktorí majú:
                            <ul class="small mt-2">
                                <li>Vyplnené telefónne číslo</li>
                                <li>Custom atribút "sms_enabled" nastavený na "1"</li>
                                <li>Aktívnu službu na vybranom zariadení</li>
                            </ul>
                        </small>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Informácie -->
                <div class="config-section">
                    <h4><i class="fas fa-info-circle"></i> Informácie o plugine</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Technické SMS:</h6>
                            <ul class="small">
                                <li>Nové tickety a zmeny stavu</li>
                                <li>Výpadky zariadení z NMS</li>
                                <li>Odosielanie technikom</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Zákaznícke SMS:</h6>
                            <ul class="small">
                                <li>Automatické pri faktúrach</li>
                                <li>Upomienky a platby</li>
                                <li>Hromadné podľa lokácie</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Posledná aktualizácia:</strong> <?= date('d.m.Y H:i:s') ?> |
                            <strong>API:</strong> https://api.smsgate.sk/json/ (GET requesty) |
                            <strong>Funkcie:</strong> Token-based + Session-based fallback + Phone validation
                        </small>
                    </div>
                </div>
                
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Počítadlo znakov pre SMS správu
        document.addEventListener('DOMContentLoaded', function() {
            const messageTextarea = document.getElementById('bulk_message');
            if (messageTextarea) {
                messageTextarea.addEventListener('input', function() {
                    const length = this.value.length;
                    const maxLength = 160;
                    const remaining = maxLength - length;
                    
                    let helpText = this.nextElementSibling;
                    if (remaining < 0) {
                        helpText.textContent = `Prekročený limit o ${Math.abs(remaining)} znakov`;
                        helpText.className = 'form-text text-danger';
                    } else {
                        helpText.textContent = `Zostáva ${remaining} znakov`;
                        helpText.className = 'form-text text-muted';
                    }
                });
            }
        });
    </script>
</body>
</html>
