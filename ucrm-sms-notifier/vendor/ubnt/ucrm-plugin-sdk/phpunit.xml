<?xml version="1.0"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" bootstrap="vendor/autoload.php" colors="true"
         convertErrorsToExceptions="true" convertNoticesToExceptions="true" convertWarningsToExceptions="true"
         stopOnFailure="true" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd">
    <coverage>
        <include>
            <directory suffix=".php">src</directory>
        </include>
        <report>
            <clover outputFile="build/logs/clover.xml"/>
        </report>
    </coverage>
    <testsuites>
        <testsuite name="unit">
            <directory suffix="Test.php">tests/</directory>
        </testsuite>
    </testsuites>
    <logging/>
</phpunit>
