{"name": "ubnt/ucrm-plugin-sdk", "description": "UCRM plugin SDK", "keywords": ["ucrm", "sdk"], "type": "library", "homepage": "https://github.com/Ubiquiti-App/UCRM-Plugin-SDK", "license": "MIT", "require": {"php": ">=8.1", "ext-curl": "*", "ext-json": "*", "guzzlehttp/guzzle": "^7.5", "symfony/filesystem": "^6.2"}, "autoload": {"psr-4": {"Ubnt\\": "src/"}}, "autoload-dev": {"classmap": ["tests/"]}, "require-dev": {"phpunit/phpunit": "^9.5", "eloquent/phony-phpunit": "^7.1", "phpstan/phpstan": "^1.10", "eloquent/phpstan-phony": "^0.8.0", "phpstan/phpstan-strict-rules": "^1.2", "symplify/easy-coding-standard": "^10.2", "php-coveralls/php-coveralls": "^2.5", "ocramius/package-versions": "^2.7"}, "suggest": {"ext-zip": "Needed for pack-plugin script."}, "bin": ["bin/pack-plugin"]}