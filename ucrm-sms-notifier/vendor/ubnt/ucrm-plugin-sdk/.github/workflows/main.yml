name: Main CI

on: push

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.1'

      - name: Validate composer.json and composer.lock
        run: composer validate --strict

      - name: Install dependencies
        run: composer update --no-interaction --no-progress

      - name: Check coding standards
        run: ./vendor/bin/ecs check src tests

      - name: Run PHPUnit tests
        run: ./vendor/bin/phpunit

      - name: Run PHPStan
        run: ./vendor/bin/phpstan analyse src tests --level max
