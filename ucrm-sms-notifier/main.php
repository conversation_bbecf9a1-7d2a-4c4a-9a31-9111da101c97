<?php

chdir(__DIR__);

require_once __DIR__ . '/vendor/autoload.php';

/**
 * UCRM SMS Notifier - Main script
 * Spracováva webhooky a background úlohy pre SMS notifikácie
 */

$log = \Ubnt\UcrmPluginSdk\Service\PluginLogManager::create();
$configManager = \Ubnt\UcrmPluginSdk\Service\PluginConfigManager::create();

try {
    $log->appendLog('SMS Notifier - Main script started');
    
    // Načítanie konfigurácie
    $config = $configManager->loadConfig();
    
    // Kontrola základnej konfigurácie
    if (empty($config['smsgate_api_token'])) {
        $log->appendLog('SMS Notifier - API token nie je nakonfigurovaný');
        exit(0);
    }
    
    // SMSGate.sk API konfigurácia
    $smsApiToken = $config['smsgate_api_token'];
    $smsApiUrl = $config['smsgate_api_url'] ?? 'https://api.smsgate.sk/v2/send';
    
    // Funkcia pre GET request s cURL
    function makeGetRequest($url, $log) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        if ($response === false || !empty($curlError)) {
            throw new Exception("cURL chyba: {$curlError}");
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Chyba pri parsovaní JSON odpovede: ' . json_last_error_msg());
        }
        
        return $result;
    }

    // Funkcia pre získanie session_id cez auth (fallback)
    function getSessionId($apiToken, $log) {
        $url = 'https://api.smsgate.sk/json/auth?' . http_build_query([
            'token' => $apiToken
        ]);
        
        $result = makeGetRequest($url, $log);
        
        if (isset($result['result']['status']) && $result['result']['status'] === 'success') {
            return $result['session_id'];
        } else {
            $error = $result['result']['description'] ?? $result['result']['code'] ?? 'Neznáma chyba';
            throw new Exception("SMSGate.sk Auth chyba: {$error}");
        }
    }

    // Funkcia pre odosielanie SMS cez SMSGate.sk API (GET requesty)
    function sendSMS($apiUrl, $apiToken, $phoneNumber, $message, $log) {
        // 1. Token-based prístup (priorita)
        $url = 'https://api.smsgate.sk/json/send_message?' . http_build_query([
            'token' => $apiToken,
            'to' => $phoneNumber,
            'text' => $message,
            'from' => 'UCRM'
        ]);
        
        try {
            $result = makeGetRequest($url, $log);
            
            // Kontrola odpovede
            if (isset($result['result']['status']) && $result['result']['status'] === 'success') {
                // Kontrola jednotlivých správ
                if (isset($result['messages']) && is_array($result['messages'])) {
                    $firstMessage = $result['messages'][0];
                    if ($firstMessage['status'] === 'success') {
                        $messageId = $firstMessage['message_id'];
                        $parts = $firstMessage['parts'];
                        $log->appendLog("SMS úspešne odoslané na {$phoneNumber} (ID: {$messageId}, časti: {$parts}): {$message}");
                        return true;
                    } else {
                        $error = $firstMessage['description'] ?? $firstMessage['code'] ?? 'Neznáma chyba';
                        throw new Exception("SMS chyba: {$error}");
                    }
                } else {
                    throw new Exception("Neočakávaný formát odpovede - chýbajú messages");
                }
            } else {
                $error = $result['result']['description'] ?? $result['result']['code'] ?? 'Neznáma chyba';
                throw new Exception("SMSGate.sk API chyba: {$error}");
            }
        } catch (Exception $e) {
            // 2. Session-based fallback
            try {
                $sessionId = getSessionId($apiToken, $log);
                
                $url = 'https://api.smsgate.sk/json/send_message?' . http_build_query([
                    'session_id' => $sessionId,
                    'to' => $phoneNumber,
                    'text' => $message,
                    'from' => 'UCRM'
                ]);
                
                $result = makeGetRequest($url, $log);
                
                if (isset($result['result']['status']) && $result['result']['status'] === 'success') {
                    if (isset($result['messages']) && is_array($result['messages'])) {
                        $firstMessage = $result['messages'][0];
                        if ($firstMessage['status'] === 'success') {
                            $messageId = $firstMessage['message_id'];
                            $parts = $firstMessage['parts'];
                            $log->appendLog("SMS úspešne odoslané na {$phoneNumber} (session-based, ID: {$messageId}, časti: {$parts}): {$message}");
                            return true;
                        } else {
                            $error = $firstMessage['description'] ?? $firstMessage['code'] ?? 'Neznáma chyba';
                            throw new Exception("SMS chyba (session-based): {$error}");
                        }
                    } else {
                        throw new Exception("Neočakávaný formát odpovede - chýbajú messages (session-based)");
                    }
                } else {
                    $error = $result['result']['description'] ?? $result['result']['code'] ?? 'Neznáma chyba';
                    throw new Exception("SMSGate.sk API chyba (session-based): {$error}");
                }
            } catch (Exception $sessionError) {
                throw new Exception("Oba prístupy zlyhali. Token-based: " . $e->getMessage() . "; Session-based: " . $sessionError->getMessage());
            }
        }
    }
    
    // Funkcia pre kontrolu pracovného času
    function isWorkingHours() {
        $hour = (int)date('H');
        $dayOfWeek = (int)date('N'); // 1 = pondelok, 7 = nedeľa
        
        // Pracovný čas: pondelok-piatok 8:00-18:00
        return ($dayOfWeek >= 1 && $dayOfWeek <= 5) && ($hour >= 8 && $hour < 18);
    }
    
    // Spracovanie argumentov príkazového riadka
    $action = $argv[1] ?? null;
    
    switch ($action) {
        case 'test_sms':
            // Test SMS odosielania
            $testNumber = $argv[2] ?? null;
            if (!$testNumber) {
                $log->appendLog('SMS Notifier - Test SMS: chýba telefónne číslo');
                exit(1);
            }
            
            $testMessage = 'UCRM SMS Notifier - Test správa ' . date('d.m.Y H:i:s');
            sendSMS($smsApiUrl, $smsApiToken, $testNumber, $testMessage, $log);
            break;
            
        case 'ticket_created':
            // Nový ticket vytvorený
            if (!$config['enable_ticket_sms']) {
                exit(0);
            }
            
            if ($config['working_hours_only'] && !isWorkingHours()) {
                $log->appendLog('SMS Notifier - Ticket SMS preskočené (mimo pracovného času)');
                exit(0);
            }
            
            $ticketId = $argv[2] ?? 'N/A';
            $ticketSubject = $argv[3] ?? 'Nový ticket';
            $priority = $argv[4] ?? 'normal';
            
            $message = "UCRM: Nový ticket #{$ticketId} - {$ticketSubject}";
            if ($priority === 'high' || $priority === 'critical') {
                $message = "KRITICKÝ! " . $message;
            }
            
            // Odoslanie SMS technikom
            $techNumbers = explode(',', $config['tech_phone_numbers'] ?? '');
            foreach ($techNumbers as $number) {
                $number = trim($number);
                if (!empty($number)) {
                    try {
                        sendSMS($smsApiUrl, $smsApiToken, $number, $message, $log);
                    } catch (Exception $e) {
                        $log->appendLog('SMS Notifier - Chyba pri odosielaní SMS: ' . $e->getMessage());
                    }
                }
            }
            break;
            
        case 'device_outage':
            // Výpadok zariadenia
            if (!$config['enable_device_outage_sms']) {
                exit(0);
            }
            
            $deviceName = $argv[2] ?? 'Neznáme zariadenie';
            $deviceType = $argv[3] ?? 'zariadenie';
            $location = $argv[4] ?? 'neznáma lokalita';
            
            $message = "UCRM: Výpadok {$deviceType} '{$deviceName}' na lokalite {$location}";
            
            // Odoslanie SMS technikom
            $techNumbers = explode(',', $config['tech_phone_numbers'] ?? '');
            foreach ($techNumbers as $number) {
                $number = trim($number);
                if (!empty($number)) {
                    try {
                        sendSMS($smsApiUrl, $smsApiToken, $number, $message, $log);
                    } catch (Exception $e) {
                        $log->appendLog('SMS Notifier - Chyba pri odosielaní SMS: ' . $e->getMessage());
                    }
                }
            }
            break;
            
        case 'customer_invoice':
            // SMS zákazníkovi o faktúre
            if (!$config['enable_customer_sms']) {
                exit(0);
            }
            
            $customerPhone = $argv[2] ?? null;
            $invoiceNumber = $argv[3] ?? 'N/A';
            $amount = $argv[4] ?? '0';
            
            if (!$customerPhone) {
                $log->appendLog('SMS Notifier - Customer SMS: chýba telefónne číslo');
                exit(1);
            }
            
            $message = "UCRM: Na váš email sme zaslali faktúru č. {$invoiceNumber} v sume {$amount}€. Ďakujeme.";
            
            try {
                sendSMS($smsApiUrl, $smsApiToken, $customerPhone, $message, $log);
            } catch (Exception $e) {
                $log->appendLog('SMS Notifier - Chyba pri odosielaní SMS zákazníkovi: ' . $e->getMessage());
            }
            break;
            
        case 'callback':
            // Spracovanie callback z SMSGate.sk
            $messageId = $_GET['message_id'] ?? $argv[2] ?? null;
            $status = $_GET['status'] ?? $argv[3] ?? null;
            $addressFrom = $_GET['addressFrom'] ?? $argv[4] ?? null;
            $addressTo = $_GET['addressTo'] ?? $argv[5] ?? null;
            $channel = $_GET['channel'] ?? $argv[6] ?? 'sms';
            
            if ($messageId && $status) {
                $log->appendLog("SMS Callback - Message ID: {$messageId}, Status: {$status}, From: {$addressFrom}, To: {$addressTo}, Channel: {$channel}");
                
                // Tu môžete pridať ďalšie spracovanie callback-u
                // Napríklad aktualizácia databázy, notifikácie, atď.
            }
            break;
            
        case 'incoming_sms':
            // Spracovanie prichádzajúcich SMS
            $addressFrom = $_GET['addressFrom'] ?? $argv[2] ?? null;
            $addressTo = $_GET['addressTo'] ?? $argv[3] ?? null;
            $timestamp = $_GET['timestamp'] ?? $argv[4] ?? date('Y-m-d H:i:s');
            $text = $_GET['text'] ?? $argv[5] ?? null;
            $channel = $_GET['channel'] ?? $argv[6] ?? 'sms';
            
            if ($addressFrom && $text) {
                $log->appendLog("Incoming SMS - From: {$addressFrom}, To: {$addressTo}, Time: {$timestamp}, Channel: {$channel}, Text: {$text}");
                
                // Tu môžete pridať spracovanie prichádzajúcich SMS
                // Napríklad automatické odpovede, ticket systém, atď.
            }
            break;
            
        default:
            $log->appendLog('SMS Notifier - Main script finished (no action specified)');
            break;
    }
    
    $log->appendLog('SMS Notifier - Main script finished successfully');
    
} catch (Exception $e) {
    $log->appendLog('SMS Notifier - Chyba v main.php: ' . $e->getMessage());
    exit(1);
}
