# Device Status Monitor Plugin - Development Guide

## Prehľad projektu

Device Status Monitor je UCRM plugin, ktorý zobrazuje stav zariadení z NMS (UISP) API v prehľadnej tabuľke s možnosťami filtrovania a exportu.

## Technická architektúra

### Štruktúra súborov

```
device-status-monitor/
├── manifest.json          # Plugin konfigur<PERSON>cia (MUSÍ byť v root!)
├── main.php              # Background script
├── public.php            # Webové rozhranie
├── composer.json         # Závislosti
├── config.json.example   # Príklad konfigurácie
├── vendor/               # Composer závislosti (KRITICKÉ!)
│   ├── autoload.php
│   └── ubnt/ucrm-plugin-sdk/
└── assets/               # Statické súbory
```

### Kľúčové technológie

- **PHP 7.1+** - Backend logika
- **UCRM Plugin SDK 0.9** - UCRM integrácia
- **Bootstrap 5.1.3** - Frontend framework
- **Font Awesome 6.0** - Ikony
- **Vanilla JavaScript** - Frontend interaktivita
- **NMS API v2.1** - Dá<PERSON> o zariadeniach

## Implementačné detaily

### 1. Manifest.json - Plugin konfigurácia

```json
{
    "version": "1",
    "information": {
        "name": "Device Status Monitor",
        "displayName": "Stav zariadení",
        "description": "Monitoring stavu zariadení z NMS API",
        "url": "https://github.com/example/device-status-monitor",
        "version": "1.0.0",
        "ucrmVersionCompliancy": {
            "min": "2.1.0",
            "max": null
        },
        "author": "Your Name"
    },
    "menu": [
        {
            "label": "Stav zariadení",
            "type": "admin",
            "target": "iframe"
        }
    ]
}
```

**Kritické body:**
- `manifest.json` MUSÍ byť v root adresári ZIP súboru
- `target: "iframe"` pre zobrazenie v UCRM rozhranie
- `type: "admin"` pre admin menu

### 2. Composer.json - Závislosti

```json
{
    "require": {
        "ubnt/ucrm-plugin-sdk": "^0.9"
    }
}
```

**Dôležité:**
- Používame SDK verziu `^0.9` (nie `^3.0`)
- Vendor adresár MUSÍ byť zahrnutý v ZIP súbore
- Autoload.php je kritický pre načítanie tried

### 3. Main.php - Background script

```php
<?php
chdir(__DIR__);
require_once __DIR__ . '/vendor/autoload.php';

$log = \Ubnt\UcrmPluginSdk\Service\PluginLogManager::create();

try {
    $log->appendLog('Device Status Monitor - Main script started');
    // Background úlohy tu
    $log->appendLog('Device Status Monitor - Main script finished successfully');
} catch (Exception $e) {
    $log->appendLog('Chyba v main.php: ' . $e->getMessage());
    exit(1);
}
```

**Pattern:**
- Vždy `chdir(__DIR__)` na začiatku
- Require autoload.php
- Použiť PluginLogManager pre logovanie
- Try-catch pre error handling

### 4. Public.php - Webové rozhranie

#### Základná štruktúra:

```php
<?php
chdir(__DIR__);
require_once __DIR__ . '/vendor/autoload.php';

// UCRM služby
$log = \Ubnt\UcrmPluginSdk\Service\PluginLogManager::create();
$security = \Ubnt\UcrmPluginSdk\Service\UcrmSecurity::create();

// Autentifikácia
$user = $security->getUser();
if (!$user) {
    die('<h1>Chyba</h1><p>Nie ste prihlásený do UCRM systému.</p>');
}

// Inicializácia premenných (KRITICKÉ!)
$devices = [];
$sites = [];
$errorMessage = null;
```

#### Konfiguračný systém:

**Problém:** Pôvodne sme používali komplexný config.json systém, ale to nefungovalo.

**Riešenie:** Jednoduchý POST formulár:

```php
if ($_POST && !empty($_POST['nms_url']) && !empty($_POST['nms_token'])) {
    $nmsUrl = rtrim($_POST['nms_url'], '/');
    $nmsToken = $_POST['nms_token'];
    $showForm = false;
    
    // API volania tu
}
```

#### NMS API integrácia:

```php
function callNmsApi($url, $token, $endpoint) {
    $fullUrl = $url . '/nms/api/v2.1' . $endpoint;
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'x-auth-token: ' . $token,
                'Content-Type: application/json',
                'Accept: application/json'
            ],
            'timeout' => 30
        ]
    ]);
    
    $response = @file_get_contents($fullUrl, false, $context);
    
    if ($response === false) {
        throw new Exception('Chyba pri volaní NMS API: ' . $fullUrl);
    }
    
    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Chyba pri parsovaní JSON odpovede z NMS API');
    }
    
    return $data;
}
```

**Kľúčové body:**
- Použiť `stream_context_create()` pre HTTP headers
- `@file_get_contents()` pre potlačenie warnings
- Vždy validovať JSON response
- Timeout 30 sekúnd

#### Bezpečné spracovanie dát:

**Problém:** PHP chyby pri konverzii času.

**Riešenie:**
```php
$lastSeen = $device['overview']['lastSeen'] ?? null;
$lastSeenFormatted = 'N/A';
if ($lastSeen && is_numeric($lastSeen) && $lastSeen > 0) {
    $lastSeenFormatted = date('d.m.Y H:i:s', intval($lastSeen / 1000));
}
```

**Pattern:**
- Vždy inicializovať premenné
- Validovať číselné hodnoty pred konverziou
- Použiť `intval()` pre bezpečnú konverziu
- Predvolené hodnoty pre chýbajúce dáta

### 5. Frontend implementácia

#### Bootstrap štruktúra:

```html
<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <!-- Header s tlačidlami -->
            <!-- Konfiguračný formulár ALEBO tabuľka -->
        </div>
    </div>
</div>
```

#### JavaScript funkcionalita:

```javascript
// Globálne premenné
let allRows = [];
let deviceTypes = new Set();

// Inicializácia
document.addEventListener('DOMContentLoaded', function() {
    initializeTable();
    updateStatistics();
    setupFilters();
});
```

**Funkcie:**
- `initializeTable()` - Načítanie riadkov a typov
- `updateStatistics()` - Počítanie stavov
- `setupFilters()` - Event listenery
- `applyFilters()` - Filtrovanie riadkov
- `exportToCsv()` - CSV export

## Riešené problémy a lessons learned

### 1. Vendor závislosti

**Problém:** "Failed to open stream: No such file or directory vendor/autoload.php"

**Príčina:** ZIP balíček neobsahoval vendor adresár.

**Riešenie:**
1. Skopírovať vendor z fungujúceho pluginu
2. Alebo spustiť `composer install`
3. Zahrnúť vendor do ZIP balíčka

### 2. Manifest.json umiestnenie

**Problém:** "Plugin manifest could not be found"

**Príčina:** Manifest.json bol v src/ podadresári.

**Riešenie:** Manifest.json MUSÍ byť v root adresári ZIP súboru.

### 3. SDK verzia kompatibilita

**Problém:** Novší SDK `^3.0` nefungoval.

**Riešenie:** Použiť starší SDK `^0.9` ako v test-plugin.

### 4. Konfiguračný systém

**Problém:** Komplexný config.json systém nefungoval.

**Riešenie:** Jednoduchý POST formulár v public.php.

### 5. PHP type errors

**Problém:** "A non-numeric value encountered" pri konverzii času.

**Riešenie:** Validácia a bezpečná konverzia s `is_numeric()` a `intval()`.

### 6. Nedefinované premenné

**Problém:** "Undefined variable $devices"

**Riešenie:** Vždy inicializovať premenné pred try blokom.

## Best practices

### 1. Štruktúra kódu

```php
// Vždy na začiatku
chdir(__DIR__);
require_once __DIR__ . '/vendor/autoload.php';

// Inicializácia služieb
$log = \Ubnt\UcrmPluginSdk\Service\PluginLogManager::create();
$security = \Ubnt\UcrmPluginSdk\Service\UcrmSecurity::create();

// Inicializácia premenných
$variable = [];
$errorMessage = null;

// Try-catch pre API volania
try {
    // API logika
} catch (Exception $e) {
    $errorMessage = $e->getMessage();
    $log->appendLog('Chyba: ' . $errorMessage);
}
```

### 2. Bezpečnosť

- Vždy validovať user input
- Použiť `htmlspecialchars()` pre output
- Kontrolovať autentifikáciu
- Logovať chyby

### 3. Error handling

- Inicializovať všetky premenné
- Validovať API responses
- Použiť try-catch bloky
- Poskytovať užitočné chybové hlášky

### 4. Frontend

- Responzívny dizajn (Bootstrap)
- Progressive enhancement
- Accessibility (ARIA labels)
- Užitočné loading states

## Deployment proces

### 1. Príprava súborov

```bash
# Štruktúra pre ZIP
device-status-monitor/
├── manifest.json     # V ROOT!
├── main.php
├── public.php
├── composer.json
├── config.json.example
├── vendor/          # MUSÍ byť zahrnuté!
└── assets/
```

### 2. Vytvorenie ZIP balíčka

```bash
cd device-status-monitor
zip -r device-status-monitor.zip . -x "*.zip" "README.md" "INSTALLATION.md" ".DS_Store" "config.json"
```

**Vylúčiť:**
- Existujúce ZIP súbory
- Dokumentáciu (README, INSTALLATION)
- System súbory (.DS_Store)
- Skutočný config.json (len example)

### 3. Overenie ZIP obsahu

```bash
unzip -l device-status-monitor.zip | head -20
```

**Kontrola:**
- Manifest.json je v root (nie v podadresári)
- Vendor adresár je prítomný
- Všetky PHP súbory sú zahrnuté

### 4. Inštalácia v UCRM

1. System → Plugins
2. Upload plugin
3. Vyberte ZIP súbor
4. Enable plugin
5. Otestujte funkcionalitu

## Testovanie a debugging

### 1. Lokálne testovanie

- Overiť PHP syntax: `php -l public.php`
- Testovať API volania mimo UCRM
- Validovať JSON responses

### 2. UCRM debugging

- Kontrolovať plugin logy v UCRM
- Použiť browser developer tools
- Testovať s rôznymi používateľmi

### 3. Časté problémy

- **Blank page:** Syntax error v PHP
- **500 error:** Chýbajúce závislosti
- **API errors:** Nesprávne tokeny/URL
- **Permission denied:** Nesprávne súborové oprávnenia

## Rozšírenia a vylepšenia

### Možné vylepšenia:

1. **Caching:** Redis/Memcached pre API responses
2. **Real-time updates:** WebSocket/SSE
3. **Advanced filtering:** Regex, date ranges
4. **Alerting:** Email/SMS notifikácie
5. **Reporting:** PDF/Excel exporty
6. **Multi-tenant:** Podpora viacerých NMS serverov

### Architektúrne zmeny:

1. **Database storage:** Pre historické dáta
2. **Background jobs:** Cron-based updates
3. **REST API:** Pre externé integrácie
4. **Plugin hooks:** Pre rozšíriteľnosť

## Aktualizácie a vylepšenia

### Verzia 1.1 - UCRM Configuration System (29.7.2025)

**Problém:** Plugin si nepamätal nastavenia medzi spusteniami - vždy zobrazoval konfiguračný formulár.

**Riešenie:** Implementácia oficiálneho UCRM konfiguračného systému.

#### Zmeny v kóde:

**1. Public.php - Konfiguračný systém:**
```php
// PRED - vlastný POST formulár
if ($_POST && !empty($_POST['nms_url']) && !empty($_POST['nms_token'])) {
    $nmsUrl = rtrim($_POST['nms_url'], '/');
    $nmsToken = $_POST['nms_token'];
    $showForm = false;
}

// PO - UCRM Plugin SDK
$configManager = \Ubnt\UcrmPluginSdk\Service\PluginConfigManager::create();
$config = $configManager->loadConfig();
$nmsUrl = rtrim($config['nms_url'] ?? '', '/');
$nmsToken = $config['nms_token'] ?? '';
$refreshInterval = (int)($config['refresh_interval'] ?? 0);
$showOfflineDevices = !empty($config['show_offline_devices']);
```

**2. Manifest.json - už obsahoval správne polia:**
```json
"configuration": [
    {
        "key": "nms_url",
        "label": "NMS Server URL",
        "description": "URL adresa vášho NMS servera",
        "required": true
    },
    {
        "key": "nms_token",
        "label": "NMS API Token", 
        "description": "API token pre prístup k NMS API",
        "required": true
    }
]
```

**3. Frontend zmeny:**
- Odstránený vlastný konfiguračný formulár
- Pridaná informačná hláška s návodom na konfiguráciu
- Upravené podmienky pre zobrazenie tlačidiel

#### Výhody nového riešenia:

✅ **Trvalé uloženie** - nastavenia sa ukladajú do UCRM databázy
✅ **Oficiálny spôsob** - používa UCRM Plugin SDK
✅ **Bezpečnosť** - API token chránený UCRM systémom
✅ **Konzistentnosť** - rovnaké rozhranie ako ostatné pluginy
✅ **Jednoduchosť** - žiadny vlastný konfiguračný kód

#### Lessons learned:

1. **UCRM má vlastný konfiguračný systém** - vždy ho použiť namiesto vlastných formulárov
2. **PluginConfigManager** je správny spôsob načítania konfigurácie
3. **Manifest.json configuration** definuje polia v UCRM admin rozhranie
4. **Trvalé uloženie** vyžaduje UCRM SDK, nie vlastné riešenia

## Záver

Device Status Monitor plugin demonštruje kľúčové koncepty UCRM plugin development:

- **Správna štruktúra** súborov a ZIP balíčka
- **UCRM SDK integrácia** pre autentifikáciu, logovanie a konfiguráciu
- **Externí API integrácia** s proper error handling
- **Moderný frontend** s Bootstrap a JavaScript
- **Bezpečné spracovanie** dát a user input
- **Oficiálny UCRM konfiguračný systém** pre trvalé nastavenia

Táto dokumentácia slúži ako referencia pre budúce UCRM plugin projekty a obsahuje všetky lessons learned z tohto projektu vrátane aktualizácií a vylepšení.
