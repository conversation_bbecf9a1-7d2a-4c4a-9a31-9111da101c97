# UCRM Plugin Development - Quick Reference

## Kritické body pre úspešný plugin

### 1. ZIP štruktúra (MUSÍ byť správne!)
```
plugin-name.zip
├── manifest.json     # V ROOT! (nie v src/)
├── main.php
├── public.php
├── composer.json
├── vendor/           # MUSÍ byť zahrnuté!
│   └── autoload.php
└── config.json.example
```

### 2. Manifest.json template
```json
{
    "version": "1",
    "information": {
        "name": "Plugin Name",
        "displayName": "Menu Label",
        "description": "Plugin description",
        "version": "1.0.0",
        "ucrmVersionCompliancy": {
            "min": "2.1.0",
            "max": null
        }
    },
    "menu": [
        {
            "label": "Menu Label",
            "type": "admin",
            "target": "iframe"
        }
    ],
    "configuration": [
        {
            "key": "config_key",
            "label": "Config Label",
            "description": "Config description",
            "required": true
        }
    ]
}
```

### 3. Composer.json (SDK verzia!)
```json
{
    "require": {
        "ubnt/ucrm-plugin-sdk": "^0.9"
    }
}
```

### 4. PHP štruktúra template
```php
<?php
chdir(__DIR__);
require_once __DIR__ . '/vendor/autoload.php';

// UCRM služby
$log = \Ubnt\UcrmPluginSdk\Service\PluginLogManager::create();
$security = \Ubnt\UcrmPluginSdk\Service\UcrmSecurity::create();
$configManager = \Ubnt\UcrmPluginSdk\Service\PluginConfigManager::create();

// Autentifikácia
$user = $security->getUser();
if (!$user) {
    die('<h1>Chyba</h1><p>Nie ste prihlásený.</p>');
}

// Načítanie konfigurácie
$config = $configManager->loadConfig();
$setting1 = $config['config_key'] ?? '';

// VŽDY inicializovať premenné!
$data = [];
$errorMessage = null;

// Kontrola konfigurácie
if (empty($setting1)) {
    $errorMessage = 'Plugin nie je nakonfigurovaný. Prejdite do System → Plugins → Plugin Name → Configuration.';
} else {
    try {
        // API logika tu
        $log->appendLog('Plugin action completed');
    } catch (Exception $e) {
        $errorMessage = $e->getMessage();
        $log->appendLog('Chyba: ' . $errorMessage);
    }
}
?>
```

### 5. UCRM konfiguračný systém
```php
// Načítanie konfigurácie
$configManager = \Ubnt\UcrmPluginSdk\Service\PluginConfigManager::create();
$config = $configManager->loadConfig();

// Prístup k hodnotám
$apiUrl = $config['api_url'] ?? '';
$apiToken = $config['api_token'] ?? '';
$refreshInterval = (int)($config['refresh_interval'] ?? 0);
$enableFeature = !empty($config['enable_feature']);

// Kontrola povinných nastavení
if (empty($apiUrl) || empty($apiToken)) {
    $errorMessage = 'Plugin nie je nakonfigurovaný...';
}
```

### 6. Bezpečné API volania
```php
function callExternalApi($url, $token, $endpoint) {
    $fullUrl = $url . $endpoint;
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Authorization: Bearer ' . $token,
                'Content-Type: application/json'
            ],
            'timeout' => 30
        ]
    ]);
    
    $response = @file_get_contents($fullUrl, false, $context);
    
    if ($response === false) {
        throw new Exception('API call failed: ' . $fullUrl);
    }
    
    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON response');
    }
    
    return $data;
}
```

### 7. Bezpečné spracovanie dát
```php
// Vždy validovať pred konverziou
$timestamp = $data['timestamp'] ?? null;
$formatted = 'N/A';
if ($timestamp && is_numeric($timestamp) && $timestamp > 0) {
    $formatted = date('d.m.Y H:i:s', intval($timestamp));
}

// Bezpečný output
echo htmlspecialchars($userInput ?? 'N/A');
```

### 8. ZIP vytvorenie
```bash
cd plugin-directory
zip -r plugin-name.zip . -x "*.zip" "README.md" ".DS_Store" "config.json"
```

### 9. Overenie ZIP
```bash
unzip -l plugin-name.zip | head -10
# Skontrolovať:
# - manifest.json je v root
# - vendor/ adresár je prítomný
# - všetky PHP súbory sú zahrnuté
```

## Časté chyby a riešenia

| Chyba | Príčina | Riešenie |
|-------|---------|----------|
| "Plugin manifest could not be found" | manifest.json nie je v root | Presunúť manifest.json do root ZIP |
| "Failed to open stream: vendor/autoload.php" | Chýba vendor adresár | Skopírovať vendor z fungujúceho pluginu |
| "Undefined variable" | Neinicializované premenné | Inicializovať pred try blokom |
| "A non-numeric value encountered" | Nevalidované číselné hodnoty | Použiť is_numeric() a intval() |
| Blank page | PHP syntax error | Skontrolovať php -l file.php |

## Debugging checklist

- [ ] PHP syntax: `php -l public.php`
- [ ] ZIP štruktúra: manifest.json v root
- [ ] Vendor adresár prítomný
- [ ] SDK verzia ^0.9
- [ ] Všetky premenné inicializované
- [ ] API tokeny/URL správne
- [ ] Error handling implementovaný
- [ ] Logovanie funguje

## Frontend best practices

```html
<!-- Bootstrap štruktúra -->
<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <h2><i class="fas fa-icon"></i> Plugin Title</h2>
            <!-- Content -->
        </div>
    </div>
</div>
```

```javascript
// JavaScript pattern
document.addEventListener('DOMContentLoaded', function() {
    // Inicializácia
});

function handleAction() {
    // Validácia
    // API call alebo form submit
    // UI update
}
```

## Deployment proces

1. **Príprava**: Skontrolovať štruktúru súborov
2. **ZIP**: Vytvoriť s správnymi exclude pravidlami
3. **Overenie**: Skontrolovať ZIP obsah
4. **Upload**: UCRM System → Plugins
5. **Test**: Aktivovať a otestovať funkcionalitu

## Užitočné príkazy

```bash
# Syntax check
find . -name "*.php" -exec php -l {} \;

# ZIP s exclude
zip -r plugin.zip . -x "*.zip" "*.md" ".DS_Store"

# ZIP obsah
unzip -l plugin.zip

# Vendor copy (ak potrebné)
cp -r ../working-plugin/vendor ./
```

Táto quick reference obsahuje všetky kritické body pre úspešný UCRM plugin development.
